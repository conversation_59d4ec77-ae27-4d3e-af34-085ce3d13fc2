.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-family: inherit;

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // Horizontal layout option
  &.radio-group--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }

  // Inline layout option
  &.radio-group--inline {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 24px;
    align-items: center;
  }
}

// Responsive design
@media (max-width: 768px) {
  .radio-group {
    &.radio-group--horizontal,
    &.radio-group--inline {
      flex-direction: column;
      gap: 8px;
    }
  }
}
