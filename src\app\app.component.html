<div class="app-container">
  <header class="app-header">
    <h1>{{ title }}</h1>
    <p>Demonstrating custom radio group and radio button components with reactive forms</p>
  </header>


  <main class="main-content">
    <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="demo-form">
      <!-- Basic Information Section -->
      <section class="form-section">
        <h2>Basic Information</h2>

        <div class="form-group">
          <label for="name">Full Name *</label>
          <input
            id="name"
            type="text"
            formControlName="name"
            class="form-input"
            [class.error]="isFieldInvalid('name')"
            placeholder="Enter your full name">
          <div class="error-message" *ngIf="isFieldInvalid('name')">
            {{ getFieldError('name') }}
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email Address *</label>
          <input
            id="email"
            type="email"
            formControlName="email"
            class="form-input"
            [class.error]="isFieldInvalid('email')"
            placeholder="Enter your email address">
          <div class="error-message" *ngIf="isFieldInvalid('email')">
            {{ getFieldError('email') }}
          </div>
        </div>

        <div class="form-group">
          <lib-radio-group>
            <lib-radio-button
              *ngFor="let option of genderOptions"
              [value]="option.value">
              {{ option.label }}
            </lib-radio-button>
          </lib-radio-group>
        </div>

        
      </section>
      </form>
  </main>
</div>

<router-outlet />
