import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
  ContentChildren,
  Query<PERSON>ist,
  AfterContentInit,
  <PERSON><PERSON><PERSON>roy,
  HostListener
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { RadioButtonComponent } from '../radio-button/radio-button.component';

@Component({
  selector: 'lib-radio-group',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './radio-group.component.html',
  styleUrls: ['./radio-group.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioGroupComponent),
      multi: true
    }
  ]
})
export class RadioGroupComponent implements ControlValueAccessor, AfterContentInit, On<PERSON><PERSON>roy {
  @Input() name: string = `radio-group-${Math.random().toString(36).substring(2, 11)}`;
  @Input() disabled: boolean = true;
  @Input() required: boolean = false;
  @Input() ariaLabel: string = '';
  @Input() ariaLabelledBy: string = '';
  @Input() errorMessage: string = '';
  @Input() showError: boolean = false;

  @Output() change = new EventEmitter<any>();

  @ContentChildren(RadioButtonComponent) radioButtons!: QueryList<RadioButtonComponent>;

  private _value: any;
  private _hasError: boolean = false;
  private destroy$ = new Subject<void>();

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  constructor() {}

  get value(): any {
    return this._value;
  }

  set value(newValue: any) {
    if (this._value !== newValue) {
      this._value = newValue;
      this.updateRadioButtons();
      this.onChange(newValue);
      this.change.emit(newValue);
    }
  }

  get hasError(): boolean {
    return this._hasError || this.showError;
  }

  set hasError(value: boolean) {
    this._hasError = value;
    this.updateRadioButtons();
  }

  ngAfterContentInit(): void {
    this.setupRadioButtons();
    
    // Listen for changes in radio buttons
    this.radioButtons.changes
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.setupRadioButtons();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ControlValueAccessor methods
  writeValue(value: any): void {
    this._value = value;
    this.updateRadioButtons();
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.updateRadioButtons();
  }

  private setupRadioButtons(): void {
    if (this.radioButtons) {
      this.radioButtons.forEach((radioButton, index) => {
        // Set the name for all radio buttons in the group
        radioButton.name = this.name;
        
        // Set initial values
        radioButton.groupValue = this._value;
        radioButton.disabled = this.disabled || radioButton.disabled;
        
        // Set tab index for keyboard navigation
        radioButton.tabIndex = this.getTabIndex(radioButton, index);
        
        // Listen to radio button changes
        radioButton.change
          .pipe(takeUntil(this.destroy$))
          .subscribe((value: any) => {
            this.value = value;
            this.updateTabIndices();
          });

        // Listen to focus events for proper tab management
        radioButton.focusEvent
          .pipe(takeUntil(this.destroy$))
          .subscribe(() => {
            this.updateTabIndices();
          });

        // Listen to blur events
        radioButton.blur
          .pipe(takeUntil(this.destroy$))
          .subscribe(() => {
            this.onTouched();
          });
      });
    }
  }

  private updateRadioButtons(): void {
    if (this.radioButtons) {
      this.radioButtons.forEach((radioButton, index) => {
        radioButton.groupValue = this._value;
        radioButton.disabled = this.disabled || radioButton.disabled;
        radioButton.tabIndex = this.getTabIndex(radioButton, index);
      });
    }
  }

  private getTabIndex(radioButton: RadioButtonComponent, index: number): number {
    // Only the selected radio button or the first one should be tabbable
    if (this._value !== undefined) {
      return radioButton.value === this._value ? 0 : -1;
    }
    return index === 0 ? 0 : -1;
  }

  private updateTabIndices(): void {
    if (this.radioButtons) {
      this.radioButtons.forEach((radioButton, index) => {
        radioButton.tabIndex = this.getTabIndex(radioButton, index);
      });
    }
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.radioButtons || this.disabled) {
      return;
    }

    const radioButtonsArray = this.radioButtons.toArray();
    const currentIndex = radioButtonsArray.findIndex(rb => rb.checked);
    let targetIndex = currentIndex;

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        event.preventDefault();
        targetIndex = (currentIndex + 1) % radioButtonsArray.length;
        break;
      case 'ArrowUp':
      case 'ArrowLeft':
        event.preventDefault();
        targetIndex = currentIndex > 0 ? currentIndex - 1 : radioButtonsArray.length - 1;
        break;
      case ' ':
      case 'Enter':
        event.preventDefault();
        const focusedElement = document.activeElement;
        const focusedRadio = radioButtonsArray.find(rb => 
          rb.elementRef.nativeElement.contains(focusedElement)
        );
        if (focusedRadio) {
          this.value = focusedRadio.value;
        }
        return;
    }

    if (targetIndex !== currentIndex && targetIndex >= 0) {
      const targetRadio = radioButtonsArray[targetIndex];
      this.value = targetRadio.value;
      targetRadio.focus();
    }
  }
}
